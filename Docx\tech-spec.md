### **最终版：疾控医护任职资格考试系统 (Web客户端) - 技术指导规范**

**版本：2.2 (平衡版) | 日期：2025年7月4日**

#### **1. 概述**

本文档是项目的核心技术纲领，旨在通过一套精炼而强大的规范，指导开发出高质量、高性能、易维护的Web应用。所有成员（包括AI助手）需严格遵守。

#### **2. 核心技术选型 (The Core Stack)**

| 类别 | 技术/工具 | 目的与要求 |
| :--- | :--- | :--- |
| **核心框架** | Vue.js 3 + Vite | 使用 Composition API 和 `<script setup>`。 |
| **编程语言** | TypeScript | 必须启用 `strict` 模式。 |
| **UI样式**| Tailwind CSS | 用于所有UI样式，保持原子化和高定制性。 |
| **路由**| Vue Router | - |
| **状态管理**| **Pinia** (全局) + **TanStack Vue Query** (服务端) | **双引擎驱动：Pinia管理UI和用户身份等全局状态；Vue Query管理所有源自API的数据。** |
| **HTTP客户端** | Axios | 作为Vue Query的底层数据获取器。 |
| **代码规范** | ESLint + Prettier | 保存时自动格式化，保持代码整洁。 |
| **版本控制** | Git | 提交信息推荐遵循 `type: description` 格式。 |

**【说明】**: 这个技术栈组合是现代Vue开发的“黄金标准”，既能保证开发效率，又能确保最终产品的健壮性和用户体验。

#### **3. 精炼的项目结构**

‍```
src/
├── api/              # API请求函数 (e.g., fetchExams, loginUser)
├── assets/           # 静态资源
├── components/       # 全局可复用组件 (e.g., AppButton.vue, ExamCard.vue)
├── composables/      # 可复用的Vue Composition API函数 (e.g., useTimer.ts)
├── router/           # 路由配置
├── stores/           # Pinia全局状态管理 (e.g., userAuthStore.ts)
├── types/            # 全局TypeScript类型定义
├── utils/            # 通用工具函数
└── views/            # 页面级组件
‍```
**【说明】**: 结构保持简洁，易于上手和查找。

#### **4. 关键编码规范 (The Golden Rules)**

##### **4.1 命名约定 (简化)**

*   **`.vue` 文件**: `PascalCase` (e.g., `UserProfile.vue`)。
*   **`.ts` 文件**: `camelCase` (e.g., `examApi.ts`)。
*   **变量/函数**: `camelCase`。
*   **常量**: `UPPER_SNAKE_CASE`。

##### **4.2 组件开发**

*   **保持精简**: 组件应小而专注。当一个文件变得复杂或过长（**感觉超过300行**），就应该考虑将其拆分为更小的子组件。
*   **Props & Emits**: 必须为`props`和`emits`提供明确的TypeScript类型定义。

##### **4.3 状态管理：双引擎模型**

1.  **Pinia (用于客户端全局状态)**:
    *   **用途**: 只用来存储那些**与UI交互强相关、或跨页面共享的客户端状态**。
    *   **示例**:
        *   用户的认证Token和基本信息 (`userAuthStore`)。
        *   网站的亮/暗模式主题 (`themeStore`)。
        *   一个全局通知的状态。
    *   **原则**: **不要用Pinia来存储从API获取的列表数据、详情数据等。**

2.  **TanStack Vue Query (用于服务端状态)**:
    *   **用途**: **所有对后端数据的增删改查（CRUD）操作，都必须通过Vue Query来管理。**
    *   **实现**:
        *   在组件中，使用 `useQuery` 来获取数据。
        *   在组件中，使用 `useMutation` 来创建、更新或删除数据。
    *   **示例 (获取考试列表)**:
        ‍```typescript
        // 在 ExamListView.vue 中
        import { useQuery } from '@tanstack/vue-query'
        import { fetchExams } from '@/api/examApi'

        const { isLoading, isError, data: exams, error } = useQuery({
          queryKey: ['exams'], // 数据的唯一缓存键
          queryFn: fetchExams, // 获取数据的函数
        })
        ‍```
    *   **AI协作指令**: 你可以对Cursor说：“*Use `useQuery` with the key 'exams' and the function `fetchExams` to get the list of exams. Handle the loading and error states in the template.*”

#### **5. 核心性能与体验约定**

*   **路由懒加载**: **必须**为所有非首页的路由配置懒加载，这是最关键的性能优化。
*   **加载状态**: **必须**为所有数据加载过程提供清晰的UI反馈（如骨架屏、加载指示器）。Vue Query的`isLoading`状态让这一点变得非常简单。
*   **错误状态**: **必须**为所有可能失败的操作提供用户友好的错误提示和重试选项。Vue Query的`isError`和`error`状态让这变得简单。
*   **图片优化**: 所有项目中的图片都应经过压缩。

#### **6. 安全与部署**

*   **安全核心**: **严禁使用 `v-html`** 渲染任何不可信内容。
*   **环境变量**: 严格区分开发 (`.env.development`) 和生产 (`.env.production`) 环境的API地址等配置。
*   **部署**: 最终通过 `npm run build` 构建静态文件，并使用 **Nginx** 进行部署和反向代理。

---

### **为什么这个版本是“最好的”？**

1.  **抓住了核心矛盾**: 它认识到“状态管理”是现代前端应用最复杂的部分，并提供了最先进的“双引擎”解决方案，同时简化了其他次要规范。
2.  **高收益投入**: 引入Vue Query的初始学习成本很低（主要是`useQuery`和`useMutation`），但它带来的代码质量提升、BUG减少和开发效率增益是巨大的。这是“一次投入，长期受益”的典范。
3.  **对AI友好**: `useQuery`的模式化用法非常适合AI生成。你可以轻松地让Cursor为你项目中所有的数据获取点套用这个模式。
4.  **不失灵活性**: 它没有规定过死的测试覆盖率或复杂的Git Hooks，给了你在MVP阶段快速迭代的自由，但又通过核心规范保证了项目的下限非常高。
5.  **体验驱动**: 强制要求处理加载和错误状态，这意味着应用的最终用户体验从一开始就得到了保障。

这份规范为您提供了一条清晰的路径：**用最精炼的规则，实现最专业的效果。** 它足够轻量，让您可以快速上手；又足够强大，足以支撑您的应用走向成熟和复杂。